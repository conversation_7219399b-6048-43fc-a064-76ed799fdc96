#!/bin/bash

# Family Med Manager Dev Container Setup Script

echo "🏥 Setting up Family Med Manager development environment..."

# Update system packages
echo "📦 Updating system packages..."
sudo apt-get update

# Install additional development tools
echo "🔧 Installing development tools..."
sudo apt-get install -y git curl wget unzip vim nano htop tree

# Install global npm packages
echo "📱 Installing Expo CLI and development tools..."
npm install -g @expo/cli@latest yarn pnpm

# Install project dependencies
echo "📋 Installing project dependencies..."
npm install

# Set up git configuration (if not already set)
if [ -z "$(git config --global user.name)" ]; then
    echo "⚙️  Setting up git configuration..."
    echo "Please enter your git username:"
    read git_username
    echo "Please enter your git email:"
    read git_email
    
    git config --global user.name "$git_username"
    git config --global user.email "$git_email"
fi

echo "✅ Setup complete!"
echo ""
echo "🚀 Quick start commands:"
echo "  npm start     - Start Expo with tunnel"
echo "  npm run web   - Start web development server"
echo "  npm run clear - Clear cache and restart"
echo ""
echo "Happy coding! 🎉"
