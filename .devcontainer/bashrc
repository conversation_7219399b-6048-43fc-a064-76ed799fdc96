# Family Med Manager Dev Container Bash Configuration

# Basic bash configuration
export PS1='\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ '

# Expo and React Native aliases
alias start="npm start"
alias web="npm run web"
alias ios="npm run ios"
alias android="npm run android"
alias expo-start="npx expo start"
alias expo-web="npx expo start --web"
alias expo-clear="npx expo start --clear"

# Development helpers
alias ll="ls -la"
alias la="ls -A"
alias l="ls -CF"
alias ..="cd .."
alias ...="cd ../.."

# Node.js and npm helpers
alias ni="npm install"
alias ns="npm start"
alias nt="npm test"
alias nb="npm run build"

# Git aliases
alias gs="git status"
alias ga="git add"
alias gc="git commit"
alias gp="git push"
alias gl="git log --oneline"

# Welcome message
echo "🏥 Welcome to Family Med Manager Development Environment!"
echo "📱 React Native Expo app ready for development"
echo ""
echo "Quick commands:"
echo "  start     - Start Expo development server"
echo "  web       - Start web development server"
echo "  expo-web  - Start Expo web server directly"
echo ""
echo "Happy coding! 🚀"
