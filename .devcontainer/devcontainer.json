{"name": "Family Med Manager - React Native Expo", "image": "mcr.microsoft.com/devcontainers/javascript-node:1-20-bullseye", "features": {"ghcr.io/devcontainers/features/common-utils:2": {"installZsh": true, "configureZshAsDefaultShell": true, "installOhMyZsh": true, "upgradePackages": true, "username": "vscode", "userUid": "automatic", "userGid": "automatic"}, "ghcr.io/devcontainers/features/git:1": {"ppa": true, "version": "latest"}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-json", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-eslint", "expo.vscode-expo-tools", "msjsdiag.vscode-react-native"], "settings": {"terminal.integrated.defaultProfile.linux": "zsh", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "typescript.preferences.importModuleSpecifier": "relative", "javascript.preferences.importModuleSpecifier": "relative"}}}, "forwardPorts": [3000, 8081, 19000, 19001, 19002], "portsAttributes": {"3000": {"label": "Web App", "onAutoForward": "notify"}, "8081": {"label": "Metro Bundler", "onAutoForward": "silent"}, "19000": {"label": "Expo DevTools", "onAutoForward": "silent"}}, "postCreateCommand": "npm install -g @expo/cli@latest && npm install && cp .devcontainer/zshrc ~/.zshrc && cp .devcontainer/bashrc ~/.bashrc", "remoteUser": "vscode"}