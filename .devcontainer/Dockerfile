FROM mcr.microsoft.com/devcontainers/javascript-node:1-20-bullseye

# Install additional system dependencies
RUN apt-get update && export DEBIAN_FRONTEND=noninteractive \
    && apt-get -y install --no-install-recommends \
        git \
        curl \
        wget \
        unzip \
        vim \
        nano \
        htop \
        tree \
    && apt-get autoremove -y && apt-get clean -y && rm -rf /var/lib/apt/lists/*

# Install global npm packages
RUN npm install -g @expo/cli@latest yarn pnpm

# Set up working directory
WORKDIR /workspaces

# Expose ports for Expo and development servers
EXPOSE 3000 8081 19000 19001 19002

# Set default user
USER vscode
