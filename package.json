{"name": "family-med-manager", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start --tunnel", "start-local": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0 expo start --web --port 3000", "web-simple": "expo start --web --port 3000", "clear": "expo start --clear", "tunnel": "expo start --tunnel"}, "dependencies": {"expo": "~54.0.0", "expo-splash-screen": "~0.28.0", "expo-status-bar": "~2.0.0", "react": "18.3.1", "react-native": "0.76.0", "react-dom": "18.3.1", "react-native-web": "~0.19.0"}, "devDependencies": {"@babel/core": "^7.25.0", "babel-preset-expo": "~11.0.0"}, "private": true}