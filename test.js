console.log("Node.js is working!");
console.log("Node version:", process.version);
console.log("Current directory:", process.cwd());

// Test if we can require expo
try {
  const expo = require('expo');
  console.log("Expo is available");
} catch (error) {
  console.log("Expo error:", error.message);
}

// Test if we can require react
try {
  const React = require('react');
  console.log("React is available");
} catch (error) {
  console.log("React error:", error.message);
}
