# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Expo
.expo/
dist/
web-build/
.expo-shared/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# Debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local
.env

# typescript
*.tsbuildinfo

# VS Code
.vscode/settings.json
.vscode/launch.json

# Temporary files
*.tmp
*.temp

# Logs
logs
*.log

# Test files
coverage/
.nyc_output/

# Build outputs
build/
dist/
