# Family Med Manager

A React Native Expo app for managing family healthcare records, appointments, and medications.

## Features

- Welcome splash screen with healthcare-themed design
- Clean, modern UI design
- Healthcare-focused branding
- Dev container support for consistent development environment

## Getting Started

### Option 1: Dev Container (Recommended)

This project includes a complete dev container setup for consistent development across different machines.

#### Prerequisites
- Docker Desktop
- Visual Studio Code
- Dev Containers extension for VS Code

#### Setup
1. Open the project in VS Code
2. When prompted, click "Reopen in Container" or use Command Palette: `Dev Containers: Reopen in Container`
3. Wait for the container to build and dependencies to install
4. The development environment will be ready with all tools pre-configured

#### Running in Dev Container
```bash
# Start Expo development server with tunnel (recommended for containers)
npm start

# Start web development server
npm run web

# Start with local network (if having tunnel issues)
npm run start-local

# Clear cache and restart
npm run clear
```

### Option 2: Local Development

#### Prerequisites
- Node.js (v20 or later)
- npm or yarn
- Expo CLI

#### Installation
1. Install dependencies:
   ```bash
   npm install
   ```

2. Install Expo CLI globally:
   ```bash
   npm install -g @expo/cli
   ```

#### Running Locally
```bash
# Start development server
npm start

# Start web server
npm run web-simple
```

## Project Structure

```
├── App.js              # Main application component with splash screen
├── app.json            # Expo configuration
├── package.json        # Dependencies and scripts
├── babel.config.js     # Babel configuration
├── assets/             # Images and other assets
└── README.md           # This file
```

## Development

The app currently displays a welcome splash screen with:
- Animated fade-in effect
- Healthcare-themed design
- Welcome message and app branding

## Next Steps

- Add navigation structure
- Implement user authentication
- Create healthcare record management features
- Add appointment scheduling
- Implement medication tracking
