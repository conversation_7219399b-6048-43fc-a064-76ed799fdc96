# Development Guide - Family Med Manager

## 🐳 Dev Container Setup (Recommended)

This project uses Dev Containers to provide a consistent development environment across different machines and operating systems.

### What's Included

- **Node.js 20** with npm, yarn, and pnpm
- **Expo CLI** pre-installed and configured
- **VS Code extensions** for React Native development
- **Shell configuration** with helpful aliases
- **Port forwarding** for development servers
- **Git configuration** and development tools

### Quick Start

1. **Prerequisites:**
   - Docker Desktop installed and running
   - Visual Studio Code
   - Dev Containers extension for VS Code

2. **Open in Container:**
   ```bash
   # Clone the repository (if not already done)
   git clone <repository-url>
   cd FamilyMedManager
   
   # Open in VS Code
   code .
   
   # When prompted, click "Reopen in Container"
   # Or use Command Palette: Dev Containers: Reopen in Container
   ```

3. **Start Development:**
   ```bash
   # The container will automatically install dependencies
   # Once ready, start the development server:
   npm start
   
   # Or start web development:
   npm run web
   ```

### Available Commands in Container

```bash
# Expo commands
npm start          # Start with tunnel (recommended for containers)
npm run start-local # Start without tunnel
npm run web        # Start web development server
npm run clear      # Clear cache and restart

# Shell aliases (available in terminal)
start              # npm start
web                # npm run web
expo-web           # npx expo start --web
expo-clear         # npx expo start --clear

# Development helpers
ll                 # ls -la
gs                 # git status
ni                 # npm install
```

### Troubleshooting Dev Container

1. **Container won't start:**
   - Ensure Docker Desktop is running
   - Try rebuilding: Command Palette → "Dev Containers: Rebuild Container"

2. **Port conflicts:**
   - The container forwards ports 3000, 8081, 19000-19002
   - Make sure these ports are available on your host machine

3. **Expo tunnel issues:**
   - Use `npm run start-local` if tunnel doesn't work
   - Check your network configuration

## 🖥️ Local Development (Alternative)

If you prefer not to use containers:

### Prerequisites
- Node.js 20+
- npm or yarn
- Expo CLI: `npm install -g @expo/cli`

### Setup
```bash
npm install
npm run web-simple
```

## 📱 Testing the App

### Web Browser
- Navigate to `http://localhost:3000` after starting the web server
- The app will display the welcome splash screen

### Mobile Device
1. Install Expo Go app on your phone
2. Start the development server with `npm start`
3. Scan the QR code with Expo Go (Android) or Camera app (iOS)

### Simulators
- **iOS Simulator:** Press `i` in the Expo CLI
- **Android Emulator:** Press `a` in the Expo CLI

## 🏗️ Project Structure

```
FamilyMedManager/
├── .devcontainer/          # Dev container configuration
│   ├── devcontainer.json   # Main container config
│   ├── Dockerfile          # Custom container setup
│   ├── setup.sh           # Setup script
│   └── shell configs       # Bash/Zsh configurations
├── .vscode/               # VS Code settings
├── App.js                 # Main application component
├── app.json              # Expo configuration
├── package.json          # Dependencies and scripts
├── babel.config.js       # Babel configuration
└── README.md            # Project documentation
```

## 🎨 Current Features

- **Welcome Splash Screen:** Healthcare-themed welcome screen
- **Responsive Design:** Works on web, iOS, and Android
- **Modern UI:** Clean, professional healthcare design
- **Development Ready:** Full dev container setup

## 🚀 Next Steps

1. **Navigation:** Add React Navigation for multiple screens
2. **Authentication:** Implement user login/signup
3. **Database:** Set up Supabase for data storage
4. **Features:** Add health records, appointments, medications
5. **Testing:** Add unit and integration tests

## 🔧 Development Tips

1. **Hot Reload:** Changes are automatically reflected in the app
2. **Debugging:** Use React Developer Tools in browser
3. **Logs:** Check the terminal for error messages
4. **Clear Cache:** Use `npm run clear` if you encounter issues

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Ensure all prerequisites are installed
3. Try rebuilding the dev container
4. Check the project's issue tracker
